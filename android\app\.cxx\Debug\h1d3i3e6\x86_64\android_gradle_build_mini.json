{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\TestApp\\grad_project\\android\\app\\.cxx\\Debug\\h1d3i3e6\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\TestApp\\grad_project\\android\\app\\.cxx\\Debug\\h1d3i3e6\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}