{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "E:/TestApp/grad_project/android/app/.cxx/Debug/h1d3i3e6/x86_64", "source": "C:/src/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}